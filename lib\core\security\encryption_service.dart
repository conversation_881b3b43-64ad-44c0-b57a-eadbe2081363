import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:pointycastle/export.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';

import '../config/app_config.dart';
import '../constants/app_constants.dart';

/// PCI-DSS Level 1 compliant encryption service
/// Implements AES-256-GCM encryption with PBKDF2 key derivation
class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  factory EncryptionService() => _instance;
  EncryptionService._internal();

  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_OAEPwithSHA_256andMGF1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  final Logger _logger = Logger();
  late final Encrypter _encrypter;
  late final Key _masterKey;
  bool _isInitialized = false;

  /// Initialize encryption service with master key
  Future<void> initialize() async {
    try {
      _masterKey = await _getOrCreateMasterKey();
      _encrypter = Encrypter(AES(_masterKey, mode: AESMode.gcm));
      _isInitialized = true;
      _logger.i('Encryption service initialized with AES-256-GCM');
    } catch (e) {
      _logger.e('Failed to initialize encryption service: $e');
      rethrow;
    }
  }

  /// Get or create master encryption key
  Future<Key> _getOrCreateMasterKey() async {
    String? keyString = await _secureStorage.read(key: 'master_encryption_key');
    
    if (keyString == null) {
      // Generate new 256-bit key
      final keyBytes = _generateSecureRandomBytes(32);
      keyString = base64Encode(keyBytes);
      await _secureStorage.write(key: 'master_encryption_key', value: keyString);
      _logger.i('Generated new master encryption key');
    }
    
    return Key(base64Decode(keyString));
  }

  /// Generate cryptographically secure random bytes
  Uint8List _generateSecureRandomBytes(int length) {
    final random = Random.secure();
    final bytes = Uint8List(length);
    for (int i = 0; i < length; i++) {
      bytes[i] = random.nextInt(256);
    }
    return bytes;
  }

  /// Encrypt sensitive data with AES-256-GCM
  Future<String> encryptData(String plaintext) async {
    if (!_isInitialized) {
      throw Exception('Encryption service not initialized');
    }

    try {
      final iv = IV(_generateSecureRandomBytes(16));
      final encrypted = _encrypter.encrypt(plaintext, iv: iv);
      
      // Combine IV and encrypted data
      final combined = {
        'iv': iv.base64,
        'data': encrypted.base64,
      };
      
      return base64Encode(utf8.encode(jsonEncode(combined)));
    } catch (e) {
      _logger.e('Encryption failed: $e');
      rethrow;
    }
  }

  /// Decrypt sensitive data
  Future<String> decryptData(String encryptedData) async {
    if (!_isInitialized) {
      throw Exception('Encryption service not initialized');
    }

    try {
      final decodedData = jsonDecode(utf8.decode(base64Decode(encryptedData)));
      final iv = IV.fromBase64(decodedData['iv']);
      final encrypted = Encrypted.fromBase64(decodedData['data']);
      
      return _encrypter.decrypt(encrypted, iv: iv);
    } catch (e) {
      _logger.e('Decryption failed: $e');
      rethrow;
    }
  }

  /// Hash password with PBKDF2 and salt
  Future<String> hashPassword(String password, {String? salt}) async {
    salt ??= base64Encode(_generateSecureRandomBytes(AppConfig.saltLength));
    
    final pbkdf2 = PBKDF2KeyDerivator(HMac(SHA256Digest(), 64));
    pbkdf2.init(Pbkdf2Parameters(
      utf8.encode(salt),
      AppConfig.keyDerivationIterations,
      32, // 256 bits
    ));
    
    final key = pbkdf2.process(utf8.encode(password));
    final hash = base64Encode(key);
    
    return '$salt:$hash';
  }

  /// Verify password against hash
  Future<bool> verifyPassword(String password, String hashedPassword) async {
    try {
      final parts = hashedPassword.split(':');
      if (parts.length != 2) return false;
      
      final salt = parts[0];
      final expectedHash = parts[1];
      
      final actualHash = await hashPassword(password, salt: salt);
      return actualHash.split(':')[1] == expectedHash;
    } catch (e) {
      _logger.e('Password verification failed: $e');
      return false;
    }
  }

  /// Generate secure PIN hash
  Future<String> hashPIN(String pin) async {
    return await hashPassword(pin);
  }

  /// Verify PIN against hash
  Future<bool> verifyPIN(String pin, String hashedPIN) async {
    return await verifyPassword(pin, hashedPIN);
  }

  /// Encrypt transaction data for offline storage
  Future<String> encryptTransactionData(Map<String, dynamic> transactionData) async {
    final jsonString = jsonEncode(transactionData);
    return await encryptData(jsonString);
  }

  /// Decrypt transaction data from offline storage
  Future<Map<String, dynamic>> decryptTransactionData(String encryptedData) async {
    final decryptedString = await decryptData(encryptedData);
    return jsonDecode(decryptedString) as Map<String, dynamic>;
  }

  /// Generate secure session token
  String generateSessionToken() {
    final bytes = _generateSecureRandomBytes(32);
    return base64Encode(bytes);
  }

  /// Generate secure API key
  String generateApiKey() {
    final bytes = _generateSecureRandomBytes(48);
    return base64Encode(bytes);
  }

  /// Encrypt sensitive user data
  Future<Map<String, String>> encryptUserData(Map<String, dynamic> userData) async {
    final encryptedData = <String, String>{};
    
    for (final entry in userData.entries) {
      if (_isSensitiveField(entry.key)) {
        encryptedData[entry.key] = await encryptData(entry.value.toString());
      } else {
        encryptedData[entry.key] = entry.value.toString();
      }
    }
    
    return encryptedData;
  }

  /// Decrypt sensitive user data
  Future<Map<String, dynamic>> decryptUserData(Map<String, String> encryptedData) async {
    final userData = <String, dynamic>{};
    
    for (final entry in encryptedData.entries) {
      if (_isSensitiveField(entry.key)) {
        userData[entry.key] = await decryptData(entry.value);
      } else {
        userData[entry.key] = entry.value;
      }
    }
    
    return userData;
  }

  /// Check if field contains sensitive data
  bool _isSensitiveField(String fieldName) {
    const sensitiveFields = [
      'pin_hash',
      'phone_number',
      'email',
      'account_number',
      'card_number',
      'cvv',
      'bank_account',
      'national_id',
      'passport_number',
    ];
    
    return sensitiveFields.contains(fieldName.toLowerCase());
  }

  /// Generate HMAC signature for API requests
  String generateHMAC(String data, String secret) {
    final key = utf8.encode(secret);
    final bytes = utf8.encode(data);
    final hmacSha256 = Hmac(sha256, key);
    final digest = hmacSha256.convert(bytes);
    return digest.toString();
  }

  /// Verify HMAC signature
  bool verifyHMAC(String data, String signature, String secret) {
    final expectedSignature = generateHMAC(data, secret);
    return expectedSignature == signature;
  }

  /// Secure data wipe
  Future<void> secureWipe(String key) async {
    try {
      await _secureStorage.delete(key: key);
      _logger.i('Securely wiped data for key: $key');
    } catch (e) {
      _logger.e('Failed to securely wipe data: $e');
    }
  }

  /// Wipe all encrypted data
  Future<void> wipeAllData() async {
    try {
      await _secureStorage.deleteAll();
      _logger.i('All encrypted data wiped');
    } catch (e) {
      _logger.e('Failed to wipe all data: $e');
    }
  }

  /// Generate checksum for data integrity
  String generateChecksum(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Verify data integrity
  bool verifyChecksum(String data, String expectedChecksum) {
    final actualChecksum = generateChecksum(data);
    return actualChecksum == expectedChecksum;
  }

  /// Encrypt file content
  Future<Uint8List> encryptFile(Uint8List fileBytes) async {
    if (!_isInitialized) {
      throw Exception('Encryption service not initialized');
    }

    try {
      final iv = IV(_generateSecureRandomBytes(16));
      final encrypted = _encrypter.encryptBytes(fileBytes, iv: iv);
      
      // Prepend IV to encrypted data
      final result = Uint8List(16 + encrypted.bytes.length);
      result.setRange(0, 16, iv.bytes);
      result.setRange(16, result.length, encrypted.bytes);
      
      return result;
    } catch (e) {
      _logger.e('File encryption failed: $e');
      rethrow;
    }
  }

  /// Decrypt file content
  Future<Uint8List> decryptFile(Uint8List encryptedBytes) async {
    if (!_isInitialized) {
      throw Exception('Encryption service not initialized');
    }

    try {
      // Extract IV from first 16 bytes
      final iv = IV(encryptedBytes.sublist(0, 16));
      final encryptedData = Encrypted(encryptedBytes.sublist(16));
      
      return Uint8List.fromList(_encrypter.decryptBytes(encryptedData, iv: iv));
    } catch (e) {
      _logger.e('File decryption failed: $e');
      rethrow;
    }
  }

  /// Get encryption status
  Map<String, dynamic> getEncryptionStatus() {
    return {
      'isInitialized': _isInitialized,
      'algorithm': AppConfig.encryptionAlgorithm,
      'keyDerivationIterations': AppConfig.keyDerivationIterations,
      'saltLength': AppConfig.saltLength,
      'ivLength': AppConfig.ivLength,
    };
  }
}
