/// Environment Configuration for Zambia Pay
/// Supports multiple environments and regional testing
/// 
/// Usage: flutter run --dart-define=ENV=sandbox --dart-define=REGION=eastern_province

class EnvironmentConfig {
  static const String _environment = String.fromEnvironment('ENV', defaultValue: 'production');
  static const String _region = String.fromEnvironment('REGION', defaultValue: 'lusaka');
  static const String _testMode = String.fromEnvironment('TEST_MODE', defaultValue: 'false');
  
  // Environment types
  static const String development = 'development';
  static const String sandbox = 'sandbox';
  static const String staging = 'staging';
  static const String production = 'production';
  
  // Zambian regions for testing
  static const String lusaka = 'lusaka';
  static const String copperbelt = 'copperbelt';
  static const String easternProvince = 'eastern_province';
  static const String southernProvince = 'southern_province';
  static const String westernProvince = 'western_province';
  static const String northernProvince = 'northern_province';
  static const String centralProvince = 'central_province';
  static const String luapulaProvince = 'luapula_province';
  static const String muchingaProvince = 'muchinga_province';
  static const String northwesternProvince = 'northwestern_province';

  // Current environment
  static String get currentEnvironment => _environment;
  static String get currentRegion => _region;
  static bool get isTestMode => _testMode.toLowerCase() == 'true';
  
  // Environment checks
  static bool get isDevelopment => _environment == development;
  static bool get isSandbox => _environment == sandbox;
  static bool get isStaging => _environment == staging;
  static bool get isProduction => _environment == production;
  
  // Regional configurations
  static Map<String, Map<String, dynamic>> get regionalConfigs => {
    lusaka: {
      'name': 'Lusaka Province',
      'timezone': 'Africa/Lusaka',
      'primary_language': 'en',
      'secondary_languages': ['nyanja', 'bemba'],
      'network_providers': ['MTN', 'AIRTEL', 'ZAMTEL'],
      'utility_providers': ['ZESCO', 'LWSC'],
      'test_phone_numbers': ['************', '************', '************'],
      'test_accounts': {
        'zesco': ['**********', '**********'],
        'lwsc': ['WTR123456', 'WTR789012'],
      },
      'connectivity': 'HIGH', // 4G/5G available
      'rural_areas': ['Chongwe', 'Kafue', 'Luangwa'],
    },
    
    copperbelt: {
      'name': 'Copperbelt Province',
      'timezone': 'Africa/Lusaka',
      'primary_language': 'bemba',
      'secondary_languages': ['en', 'nyanja'],
      'network_providers': ['MTN', 'AIRTEL', 'ZAMTEL'],
      'utility_providers': ['ZESCO', 'NWASCO'],
      'test_phone_numbers': ['************', '************', '************'],
      'test_accounts': {
        'zesco': ['**********', '**********'],
        'nwasco': ['CB123456', 'CB789012'],
      },
      'connectivity': 'HIGH',
      'rural_areas': ['Chililabombwe', 'Chingola', 'Mufulira'],
    },
    
    easternProvince: {
      'name': 'Eastern Province',
      'timezone': 'Africa/Lusaka',
      'primary_language': 'nyanja',
      'secondary_languages': ['en', 'tumbuka'],
      'network_providers': ['MTN', 'AIRTEL'],
      'utility_providers': ['ZESCO'],
      'test_phone_numbers': ['************', '************'],
      'test_accounts': {
        'zesco': ['**********', '**********'],
      },
      'connectivity': 'MEDIUM', // 3G/4G limited
      'rural_areas': ['Chipata', 'Katete', 'Lundazi', 'Mambwe'],
      'challenges': ['poor_connectivity', 'seasonal_floods', 'low_literacy'],
    },
    
    southernProvince: {
      'name': 'Southern Province',
      'timezone': 'Africa/Lusaka',
      'primary_language': 'tonga',
      'secondary_languages': ['en', 'lozi'],
      'network_providers': ['MTN', 'AIRTEL', 'ZAMTEL'],
      'utility_providers': ['ZESCO'],
      'test_phone_numbers': ['************', '************', '************'],
      'test_accounts': {
        'zesco': ['**********', '**********'],
      },
      'connectivity': 'MEDIUM',
      'rural_areas': ['Livingstone', 'Choma', 'Mazabuka', 'Monze'],
      'challenges': ['drought_prone', 'agricultural_dependency'],
    },
    
    westernProvince: {
      'name': 'Western Province',
      'timezone': 'Africa/Lusaka',
      'primary_language': 'lozi',
      'secondary_languages': ['en'],
      'network_providers': ['MTN', 'AIRTEL'],
      'utility_providers': ['ZESCO'],
      'test_phone_numbers': ['************', '************'],
      'test_accounts': {
        'zesco': ['**********', '**********'],
      },
      'connectivity': 'LOW', // 2G/3G only
      'rural_areas': ['Mongu', 'Senanga', 'Kalabo', 'Shangombo'],
      'challenges': ['very_poor_connectivity', 'seasonal_flooding', 'remote_locations'],
    },
  };

  // Environment-specific configurations
  static Map<String, dynamic> get environmentConfig {
    switch (_environment) {
      case development:
        return {
          'api_base_url': 'http://localhost:3000',
          'debug_mode': true,
          'logging_level': 'DEBUG',
          'mock_services': true,
          'offline_mode_simulation': true,
          'test_data_enabled': true,
          'encryption_key_rotation_days': 1, // Fast rotation for testing
          'compliance_checks_enabled': false,
        };
        
      case sandbox:
        return {
          'api_base_url': 'https://sandbox-api.zambiapay.com',
          'debug_mode': true,
          'logging_level': 'INFO',
          'mock_services': false,
          'offline_mode_simulation': true,
          'test_data_enabled': true,
          'encryption_key_rotation_days': 7,
          'compliance_checks_enabled': true,
          'mtn_api_url': 'https://sandbox.momodeveloper.mtn.com',
          'airtel_api_url': 'https://openapiuat.airtel.africa',
          'zesco_api_url': 'https://sandbox.zesco.co.zm/api',
        };
        
      case staging:
        return {
          'api_base_url': 'https://staging-api.zambiapay.com',
          'debug_mode': false,
          'logging_level': 'WARN',
          'mock_services': false,
          'offline_mode_simulation': false,
          'test_data_enabled': false,
          'encryption_key_rotation_days': 30,
          'compliance_checks_enabled': true,
          'mtn_api_url': 'https://sandbox.momodeveloper.mtn.com',
          'airtel_api_url': 'https://openapiuat.airtel.africa',
          'zesco_api_url': 'https://staging.zesco.co.zm/api',
        };
        
      case production:
        return {
          'api_base_url': 'https://api.zambiapay.com',
          'debug_mode': false,
          'logging_level': 'ERROR',
          'mock_services': false,
          'offline_mode_simulation': false,
          'test_data_enabled': false,
          'encryption_key_rotation_days': 90,
          'compliance_checks_enabled': true,
          'mtn_api_url': 'https://momodeveloper.mtn.com',
          'airtel_api_url': 'https://openapi.airtel.africa',
          'zesco_api_url': 'https://api.zesco.co.zm',
        };
        
      default:
        return environmentConfig; // Fallback to development
    }
  }

  // Regional configuration
  static Map<String, dynamic> get regionConfig {
    return regionalConfigs[_region] ?? regionalConfigs[lusaka]!;
  }

  // Test scenarios based on region
  static Map<String, dynamic> get testScenarios {
    final region = regionConfig;
    final challenges = region['challenges'] as List<dynamic>? ?? [];
    
    return {
      'connectivity_tests': {
        'high_connectivity': region['connectivity'] == 'HIGH',
        'medium_connectivity': region['connectivity'] == 'MEDIUM',
        'low_connectivity': region['connectivity'] == 'LOW',
        'offline_mode': challenges.contains('poor_connectivity'),
      },
      
      'language_tests': {
        'primary_language': region['primary_language'],
        'secondary_languages': region['secondary_languages'],
        'voice_guidance_test': true,
        'sms_localization_test': true,
      },
      
      'utility_tests': {
        'providers': region['utility_providers'],
        'test_accounts': region['test_accounts'],
        'auto_alerts_test': true,
        'offline_receipts_test': true,
      },
      
      'rural_resilience_tests': {
        'sms_tokens': challenges.contains('poor_connectivity'),
        'flood_simulation': challenges.contains('seasonal_floods'),
        'drought_mode': challenges.contains('drought_prone'),
        'emergency_airtime': true,
      },
      
      'financial_inclusion_tests': {
        'basic_kyc_only': challenges.contains('low_literacy'),
        'tiered_limits': true,
        'chilimba_groups': true,
        'community_guarantors': true,
      },
    };
  }

  // Phase 1 testing configuration
  static Map<String, dynamic> get phase1TestConfig => {
    'duration_days': 30,
    'target_users': 100,
    'focus_areas': [
      'rural_resilience',
      'voice_guided_ui',
      'offline_functionality',
      'sms_tokens',
      'utility_payments',
      'basic_kyc',
    ],
    'success_metrics': {
      'user_registration_rate': 0.8, // 80% of invited users register
      'transaction_success_rate': 0.95, // 95% of transactions succeed
      'offline_sync_success_rate': 0.9, // 90% of offline transactions sync
      'voice_guidance_usage_rate': 0.6, // 60% use voice guidance
      'sms_token_redemption_rate': 0.85, // 85% of SMS tokens redeemed
      'utility_payment_success_rate': 0.92, // 92% of utility payments succeed
    },
    'test_data': {
      'users': _generateTestUsers(),
      'transactions': _generateTestTransactions(),
      'utility_accounts': _generateTestUtilityAccounts(),
    },
  };

  // Generate test users for current region
  static List<Map<String, dynamic>> _generateTestUsers() {
    final region = regionConfig;
    final testPhones = region['test_phone_numbers'] as List<dynamic>;
    final primaryLang = region['primary_language'] as String;
    
    return List.generate(10, (index) => {
      'id': 'test_user_${_region}_$index',
      'phone_number': testPhones[index % testPhones.length],
      'preferred_language': index < 5 ? primaryLang : 'en',
      'kyc_tier': index < 3 ? 'BASIC' : (index < 7 ? 'INTERMEDIATE' : 'FULL'),
      'region': _region,
      'rural_area': index % 3 == 0,
      'low_literacy': index % 4 == 0,
    });
  }

  // Generate test transactions
  static List<Map<String, dynamic>> _generateTestTransactions() {
    return [
      {
        'type': 'SEND_MONEY',
        'amount': 50.0,
        'description': 'Basic money transfer test',
      },
      {
        'type': 'BILL_PAYMENT',
        'amount': 150.0,
        'description': 'ZESCO bill payment test',
        'utility': 'ZESCO',
      },
      {
        'type': 'AIRTIME_PURCHASE',
        'amount': 25.0,
        'description': 'Airtime purchase test',
      },
      {
        'type': 'EMERGENCY_AIRTIME',
        'amount': 10.0,
        'description': 'Emergency airtime advance test',
      },
    ];
  }

  // Generate test utility accounts
  static List<Map<String, dynamic>> _generateTestUtilityAccounts() {
    final region = regionConfig;
    final testAccounts = region['test_accounts'] as Map<String, dynamic>;
    
    final accounts = <Map<String, dynamic>>[];
    
    testAccounts.forEach((provider, accountList) {
      for (final account in accountList) {
        accounts.add({
          'provider': provider.toUpperCase(),
          'account_number': account,
          'customer_name': 'Test Customer ${account.substring(account.length - 4)}',
          'amount_due': 100.0 + (account.hashCode % 300),
          'due_date': DateTime.now().add(Duration(days: 5 + (account.hashCode % 10))),
        });
      }
    });
    
    return accounts;
  }

  // Get API configuration for current environment
  static Map<String, String> get apiConfig {
    final config = environmentConfig;
    return {
      'base_url': config['api_base_url'],
      'mtn_api_url': config['mtn_api_url'] ?? '',
      'airtel_api_url': config['airtel_api_url'] ?? '',
      'zesco_api_url': config['zesco_api_url'] ?? '',
    };
  }

  // Get feature flags for current environment
  static Map<String, bool> get featureFlags {
    final config = environmentConfig;
    return {
      'debug_mode': config['debug_mode'] ?? false,
      'mock_services': config['mock_services'] ?? false,
      'offline_mode_simulation': config['offline_mode_simulation'] ?? false,
      'test_data_enabled': config['test_data_enabled'] ?? false,
      'compliance_checks_enabled': config['compliance_checks_enabled'] ?? true,
      'voice_guidance_enabled': true,
      'sms_tokens_enabled': true,
      'chilimba_enabled': true,
      'auto_alerts_enabled': true,
      'tiered_kyc_enabled': true,
    };
  }

  // Get logging configuration
  static Map<String, dynamic> get loggingConfig {
    final config = environmentConfig;
    return {
      'level': config['logging_level'] ?? 'INFO',
      'console_output': isDevelopment || isSandbox,
      'file_output': !isDevelopment,
      'remote_logging': isStaging || isProduction,
      'max_log_files': 10,
      'max_file_size_mb': 50,
    };
  }

  // Validate environment configuration
  static bool validateConfiguration() {
    try {
      // Check if region is supported
      if (!regionalConfigs.containsKey(_region)) {
        print('Warning: Unsupported region $_region, falling back to lusaka');
        return false;
      }

      // Check if environment is valid
      if (![development, sandbox, staging, production].contains(_environment)) {
        print('Error: Invalid environment $_environment');
        return false;
      }

      print('✅ Environment: $_environment');
      print('✅ Region: ${regionConfig['name']}');
      print('✅ Primary Language: ${regionConfig['primary_language']}');
      print('✅ Connectivity: ${regionConfig['connectivity']}');
      
      if (isTestMode) {
        print('✅ Test Mode: Enabled');
      }

      return true;
    } catch (e) {
      print('❌ Configuration validation failed: $e');
      return false;
    }
  }

  // Print current configuration summary
  static void printConfigurationSummary() {
    print('\n🇿🇲 Zambia Pay - Configuration Summary');
    print('=====================================');
    print('Environment: $_environment');
    print('Region: ${regionConfig['name']}');
    print('Primary Language: ${regionConfig['primary_language']}');
    print('Connectivity Level: ${regionConfig['connectivity']}');
    print('Test Mode: ${isTestMode ? 'Enabled' : 'Disabled'}');
    print('Debug Mode: ${featureFlags['debug_mode']}');
    print('Mock Services: ${featureFlags['mock_services']}');
    print('API Base URL: ${apiConfig['base_url']}');
    
    if (regionConfig['challenges'] != null) {
      print('Regional Challenges: ${(regionConfig['challenges'] as List).join(', ')}');
    }
    
    print('=====================================\n');
  }
}
