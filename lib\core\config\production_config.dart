/// Production Configuration for Pay Mule Zambia
/// Contains production API endpoints and credentials for live deployment
/// 
/// SECURITY WARNING: Replace placeholder credentials with actual production values
/// before deployment. Never commit real production credentials to version control.

import 'package:logger/logger.dart';

class ProductionConfig {
  static final Logger _logger = Logger();
  
  // Production API Base URLs
  static const String mtnProductionBaseUrl = 'https://momodeveloper.mtn.com';
  static const String airtelProductionBaseUrl = 'https://openapi.airtel.africa';
  static const String lupiyaProductionBaseUrl = 'https://api.lupiya.com';
  static const String zescoProductionBaseUrl = 'https://api.zesco.co.zm';
  
  // Production API Endpoints
  static const String tokenEndpoint = '/collection/token/';
  static const String requestToPayEndpoint = '/collection/v1_0/requesttopay';
  static const String transactionStatusEndpoint = '/collection/v1_0/requesttopay';
  static const String balanceEndpoint = '/collection/v1_0/account/balance';
  
  // Production Credentials (REPLACE WITH ACTUAL VALUES)
  static const Map<String, String> mtnProductionCredentials = {
    'subscriptionKey': 'REPLACE_WITH_ACTUAL_MTN_SUBSCRIPTION_KEY',
    'apiUserId': 'REPLACE_WITH_ACTUAL_MTN_API_USER_ID',
    'apiKey': 'REPLACE_WITH_ACTUAL_MTN_API_KEY',
    'targetEnvironment': 'mtnglobalapi',
    'callbackUrl': 'https://api.paymule.zm/callbacks/mtn',
  };
  
  static const Map<String, String> airtelProductionCredentials = {
    'clientId': 'REPLACE_WITH_ACTUAL_AIRTEL_CLIENT_ID',
    'clientSecret': 'REPLACE_WITH_ACTUAL_AIRTEL_CLIENT_SECRET',
    'apiKey': 'REPLACE_WITH_ACTUAL_AIRTEL_API_KEY',
    'callbackUrl': 'https://api.paymule.zm/callbacks/airtel',
  };
  
  static const Map<String, String> lupiyaProductionCredentials = {
    'merchantId': 'REPLACE_WITH_ACTUAL_LUPIYA_MERCHANT_ID',
    'apiKey': 'REPLACE_WITH_ACTUAL_LUPIYA_API_KEY',
    'secretKey': 'REPLACE_WITH_ACTUAL_LUPIYA_SECRET_KEY',
    'callbackUrl': 'https://api.paymule.zm/callbacks/lupiya',
  };

  // Production Transaction Limits (Bank of Zambia approved)
  static const Map<String, double> transactionLimits = {
    'daily_limit': 50000.0,        // K50,000 per day
    'monthly_limit': 500000.0,     // K500,000 per month
    'single_transaction': 25000.0,  // K25,000 per transaction
    'minimum_amount': 1.0,         // K1 minimum
  };

  /// Get production configuration for specified provider
  static Map<String, String> getProductionConfig(String provider) {
    switch (provider.toUpperCase()) {
      case 'MTN':
        _logger.i('Loading MTN production configuration');
        return {
          'baseUrl': mtnProductionBaseUrl,
          ...mtnProductionCredentials,
        };
      
      case 'AIRTEL':
        _logger.i('Loading Airtel production configuration');
        return {
          'baseUrl': airtelProductionBaseUrl,
          ...airtelProductionCredentials,
        };
      
      case 'LUPIYA':
        _logger.i('Loading Lupiya production configuration');
        return {
          'baseUrl': lupiyaProductionBaseUrl,
          ...lupiyaProductionCredentials,
        };
      
      default:
        _logger.e('Unknown provider: $provider');
        throw ArgumentError('Unsupported provider: $provider');
    }
  }

  /// Validate production credentials are properly configured
  static bool validateProductionCredentials() {
    try {
      _logger.i('Validating production credentials...');
      
      // Check MTN credentials
      if (mtnProductionCredentials.values.any((value) => value.startsWith('REPLACE_WITH'))) {
        _logger.e('MTN production credentials not configured');
        return false;
      }
      
      // Check Airtel credentials
      if (airtelProductionCredentials.values.any((value) => value.startsWith('REPLACE_WITH'))) {
        _logger.e('Airtel production credentials not configured');
        return false;
      }
      
      // Check Lupiya credentials
      if (lupiyaProductionCredentials.values.any((value) => value.startsWith('REPLACE_WITH'))) {
        _logger.e('Lupiya production credentials not configured');
        return false;
      }
      
      _logger.i('✅ All production credentials validated');
      return true;
      
    } catch (e) {
      _logger.e('Production credentials validation error: $e');
      return false;
    }
  }

  /// Get production headers for API requests
  static Map<String, String> getProductionHeaders(String provider) {
    final config = getProductionConfig(provider);
    
    switch (provider.toUpperCase()) {
      case 'MTN':
        return {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer YOUR_ACCESS_TOKEN', // Will be replaced with actual token
          'X-Reference-Id': _generateTransactionId(),
          'X-Target-Environment': config['targetEnvironment']!,
          'Ocp-Apim-Subscription-Key': config['subscriptionKey']!,
        };
      
      case 'AIRTEL':
        return {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer YOUR_ACCESS_TOKEN', // Will be replaced with actual token
          'X-Country': 'ZM',
          'X-Currency': 'ZMW',
        };
      
      default:
        return {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer YOUR_ACCESS_TOKEN',
        };
    }
  }

  /// Generate production transaction ID
  static String _generateTransactionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'PM_TX_$timestamp';
  }

  /// Production deployment checklist
  static List<String> getProductionChecklist() {
    return [
      '✓ Replace all placeholder credentials with actual production values',
      '✓ Configure production API endpoints',
      '✓ Set up production database connections',
      '✓ Enable production logging and monitoring',
      '✓ Configure production SSL certificates',
      '✓ Set up production callback URLs',
      '✓ Test all payment provider integrations',
      '✓ Verify Bank of Zambia compliance settings',
      '✓ Enable production security features',
      '✓ Configure production backup systems',
      '✓ Set up production monitoring and alerts',
      '✓ Verify production transaction limits',
    ];
  }
}
